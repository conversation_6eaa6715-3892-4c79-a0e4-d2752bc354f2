<section class="tour_category_section product_category_section w-100 d-inline-block">
    <div class="custom_container">
        <div class="feature_section_header d-flex align-items-center justify-content-center w-100 flex-wrap flex-column">
            <small>Travel Africa Destinations by Country</small>
            <h5>Discover Your Perfect African Adventure</h5>

        </div>


        <!-- Skeleton Loader (show during loading) -->
        <div id="country-skeleton" class="skeleton-wrapper">
            <?php for($i = 0; $i < 4; $i++): ?>
                <div class="skeleton-box">
                <div class="skeleton-item"> </div>
        </div>
        <?php endfor; ?>
    </div>
    <div class="product_category_list product_category_list_show w-100 position-relative" style="display: none;">
        <div class="country_arrow_box"></div>
        <ul class="product_country_slider">
            <?php $__currentLoopData = $destinationCountries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li class="h-100">
                <a href="<?php echo e(route('website.destinations.byCountry', ['country' => Str::slug($country->destination_country)])); ?>"
                    class="position-relative w-100 float-left">

                    <img width="300" height="300"
                        src="<?php echo e($country->cover_img ? asset('storage/destinations/'.$country->cover_img) : asset('website/images/logo.webp')); ?>"
                        data-src="<?php echo e($country->cover_img ? asset('storage/destinations/'.$country->cover_img) : asset('website/images/logo.webp')); ?>"
                        class="lazyload w-100 h-100 object-fit-cover"
                        alt="<?php echo e($country->alt_text_cover_image ?? $country->destination_country); ?>"
                        loading="lazy" decoding="async" />

                    <figcaption class="position-absolute w-100 bottom-0 left-0 d-flex justify-content-between align-items-end">
                        <span><?php echo e($country->destination_country); ?></span>
                    </figcaption>
                </a>
            </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    </div>
    <div class="tour_category_btn d-flex align-items-center justify-content-center w-100"> <a href="<?php echo e(route('website.destinationCountry.destinationCountry')); ?>">View All</a></div>
    </div>
</section><?php /**PATH D:\clients project\yared\travelafrica\resources\views/website/destinationCountry/countryPartial.blade.php ENDPATH**/ ?>